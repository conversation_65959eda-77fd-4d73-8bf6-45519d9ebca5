package com.scube.calculation.mapper;

import com.scube.calculation.dto.cart.AdditionalFeeDto;
import com.scube.calculation.dto.cart.AdditionalItemFeeDto;
import com.scube.calculation.dto.fee.FeeDto;
import com.scube.calculation.dto.order.OrderDto;
import com.scube.calculation.dto.order.OrderInvoiceResponse;
import com.scube.calculation.dto.order.OrderItemDto;
import com.scube.calculation.dto.order.OrderItemFeeDto;
import com.scube.calculation.model.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public abstract class OrderMapper {
    @Mapping(target = "orderId", source = "id")
    public abstract OrderDto toDto(Order order);

    public abstract List<OrderDto> toDto(List<Order> orders);

    @Mapping(target = "id", source = "orderId")
    public abstract Order toEntity(OrderDto order);

    public abstract List<Order> toEntity(List<OrderDto> orders);

    public OrderInvoiceResponse toOrderInvoiceResponse(Order order) {
        var orderDto = toDto(order);
        return new OrderInvoiceResponse(orderDto);
    }

    @Mapping(target = "fees", source = "orderItemFees")
    @Mapping(target = "orderItemUuid", source = "uuid")
    public abstract OrderItemDto toDto(OrderItem orderItem);

    public abstract FeeDto toDto(Fee fee);

    @Mapping(target = "price", expression = "java(orderItemFee.calculatePrice())")
    @Mapping(target = "orderItemFeeUuid", source = "uuid")
    public abstract OrderItemFeeDto toDto(OrderItemFee orderItemFee);

    @Mapping(target = "fees", source = "additionalItemFees")
    @Mapping(target = "price", expression = "java(orderAdditionalFee.calculatePrice())")
    @Mapping(target = "orderAdditionalFeeUuid", source = "uuid")
    public abstract AdditionalFeeDto toDto(OrderAdditionalFee orderAdditionalFee);

    @Mapping(target = "price", expression = "java(orderAdditionalItemFee.calculatePrice())")
    @Mapping(target = "orderAdditionalItemFeeUuid", source = "uuid")
    public abstract AdditionalItemFeeDto toDto(OrderAdditionalItemFee orderAdditionalItemFee);
}