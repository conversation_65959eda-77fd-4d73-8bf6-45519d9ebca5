import React from "react";
import { useMachineContext } from "../../providers/MachineProvider";
import { cn } from "@/lib/utils";
import {
  ButtonAction,
  NavigationConfig,
  NavigationLink,
} from "../../types/FormBuilderTypes3";
import { 
  CheckCircle2, 
  XCircle, 
  ArrowRight, 
  Link as LinkIcon, 
  Zap, 
  Shield,
  Mouse,
  AlertTriangle,
  CheckSquare,
  ChevronDown,
  ChevronRight,
  Star,
  Target,
  Activity
} from "lucide-react";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

const Guards = () => {
  const { currentPage, checkGuardConditions } = useMachineContext();

  // Helper to check if navigation item is currently enabled
  const isNavigationEnabled = (nav: NavigationConfig): boolean => {
    if (nav.type === "link") return true; // Links are always enabled
    if (nav.type === "button" && nav.action) {
      return nav.action.guard ? checkGuardConditions(nav.action.guard) : true;
    }
    return false;
  };

  // Get current active path summary
  const getActivePathSummary = () => {
    if (!currentPage?.navigation?.length) return null;
    
    const enabledItems = currentPage.navigation.filter(isNavigationEnabled);
    const disabledItems = currentPage.navigation.filter(nav => !isNavigationEnabled(nav));
    
    return { enabledItems, disabledItems };
  };

  const renderGuardStatus = (guard: any, passed: boolean) => (
    <div className={cn(
      "flex items-center gap-2 p-3 rounded-lg border-l-4",
      passed 
        ? "bg-green-50 border-l-green-500 text-green-800" 
        : "bg-red-50 border-l-red-500 text-red-800"
    )}>
      {passed ? (
        <CheckCircle2 className="w-4 h-4 text-green-600" />
      ) : (
        <XCircle className="w-4 h-4 text-red-600" />
      )}
      <div className="flex-1">
        <div className="flex items-center gap-2 text-sm font-medium">
          <Shield className="w-3 h-3" />
          Guard: {guard.field}
        </div>
        <div className="text-xs mt-1 space-y-1">
          <div>Value: <code className="bg-black/10 px-1 rounded text-xs">
            {typeof guard.value === "boolean" 
              ? guard.value ? "true" : "false"
              : String(guard.value)
            }
          </code></div>
          <div>Match: <span className="font-mono">{guard.matchType}</span></div>
        </div>
      </div>
      <div className={cn(
        "px-2 py-1 rounded text-xs font-medium",
        passed ? "bg-green-200 text-green-800" : "bg-red-200 text-red-800"
      )}>
        {passed ? "PASS" : "FAIL"}
      </div>
    </div>
  );

  const renderAction = (
    action: ButtonAction,
    label: string,
    level: number = 0,
  ) => {
    const guardCheck = action.guard ? checkGuardConditions(action.guard) : true;
    const indent = level * 16;

    return (
      <div key={label} className="space-y-3" style={{ marginLeft: `${indent}px` }}>
        {/* Guard Check Display */}
        {action?.guard && (
          <Accordion type="single" collapsible className="w-full">
            <AccordionItem value={`guard-${label}`}>
              <AccordionTrigger className="py-2">
                <div className="flex items-center gap-2">
                  <Shield className="w-4 h-4" />
                  <span className="text-sm font-medium">
                    Guard Condition {guardCheck ? "✅" : "❌"}
                  </span>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                {renderGuardStatus(action.guard, guardCheck)}
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        )}

        {/* Action Cards */}
        <div className="space-y-2">
          {/* Go to Page */}
          {action?.goToPage && (
            <div className={cn(
              "flex items-center gap-3 p-3 rounded-lg border-2",
              guardCheck 
                ? "bg-blue-50 border-blue-400 text-blue-800 shadow-md" 
                : "bg-gray-50 border-gray-200 text-gray-500 opacity-60"
            )}>
              {guardCheck && <Star className="w-4 h-4 text-blue-600 animate-pulse" />}
              <ArrowRight className={cn(
                "w-4 h-4",
                guardCheck ? "text-blue-600" : "text-gray-400"
              )} />
              <div className="flex-1">
                <div className={cn(
                  "font-medium text-sm",
                  guardCheck && "font-bold"
                )}>
                  Navigate to Page {guardCheck && "🎯"}
                </div>
                <div className="text-xs mt-1">
                  <code className="bg-black/10 px-1 rounded">{action.goToPage}</code>
                </div>
              </div>
              <div className={cn(
                "px-2 py-1 rounded text-xs font-bold",
                guardCheck 
                  ? "bg-blue-600 text-white animate-pulse" 
                  : "bg-gray-200 text-gray-600"
              )}>
                {guardCheck ? "ACTIVE PATH" : "DISABLED"}
              </div>
            </div>
          )}

          {/* API Call */}
          {action?.callApi && (
            <div className={cn(
              "flex items-center gap-3 p-3 rounded-lg border-2",
              guardCheck 
                ? "bg-purple-50 border-purple-400 text-purple-800 shadow-md" 
                : "bg-gray-50 border-gray-200 text-gray-500 opacity-60"
            )}>
              {guardCheck && <Star className="w-4 h-4 text-purple-600 animate-pulse" />}
              <Zap className={cn(
                "w-4 h-4",
                guardCheck ? "text-purple-600" : "text-gray-400"
              )} />
              <div className="flex-1">
                <div className={cn(
                  "font-medium text-sm",
                  guardCheck && "font-bold"
                )}>
                  API Call {guardCheck && "⚡"}
                </div>
                <div className="text-xs mt-1">
                  <code className="bg-black/10 px-1 rounded">{action.callApi}</code>
                </div>
              </div>
              <div className={cn(
                "px-2 py-1 rounded text-xs font-bold",
                guardCheck 
                  ? "bg-purple-600 text-white animate-pulse" 
                  : "bg-gray-200 text-gray-600"
              )}>
                {guardCheck ? "WILL EXECUTE" : "DISABLED"}
              </div>
            </div>
          )}
        </div>

        {/* Nested Actions with Accordions */}
        {(action.onSuccess || action.onError) && (
          <Accordion type="multiple" className="w-full">
            {action.onSuccess && (
              <AccordionItem value={`success-${label}`}>
                <AccordionTrigger className="py-2">
                  <div className={cn(
                    "flex items-center gap-2",
                    guardCheck ? "text-green-700" : "text-gray-500"
                  )}>
                    <CheckSquare className="w-4 h-4" />
                    <span className="text-sm font-medium">
                      On Success Actions {guardCheck && "🎯"}
                    </span>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className={cn(
                    "border-l-2 pl-4 space-y-2 mt-2",
                    guardCheck ? "border-l-green-400" : "border-l-gray-300"
                  )}>
                    {renderAction(action.onSuccess, `${label} - Success`, level + 1)}
                  </div>
                </AccordionContent>
              </AccordionItem>
            )}

            {action.onError && (
              <AccordionItem value={`error-${label}`}>
                <AccordionTrigger className="py-2">
                  <div className={cn(
                    "flex items-center gap-2",
                    !guardCheck ? "text-red-700" : "text-gray-500"
                  )}>
                    <AlertTriangle className="w-4 h-4" />
                    <span className="text-sm font-medium">
                      On Error Actions {!guardCheck && "🎯"}
                    </span>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className={cn(
                    "border-l-2 pl-4 space-y-2 mt-2",
                    !guardCheck ? "border-l-red-400" : "border-l-gray-300"
                  )}>
                    {renderAction(action.onError, `${label} - Error`, level + 1)}
                  </div>
                </AccordionContent>
              </AccordionItem>
            )}
          </Accordion>
        )}
      </div>
    );
  };

  if (!currentPage?.navigation?.length) {
    return (
      <div className="p-4 text-center text-gray-500 bg-gray-50 rounded-lg">
        <Mouse className="w-6 h-6 mx-auto mb-2 text-gray-400" />
        <div className="text-sm">No navigation options available</div>
      </div>
    );
  }

  const pathSummary = getActivePathSummary();

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2 text-lg font-semibold text-slate-800">
        <Shield className="w-5 h-5 text-blue-600" />
        Navigation Flow
      </div>
      
      {/* Current Path Summary */}
      {pathSummary && (
        <div className="bg-gradient-to-r from-emerald-50 to-blue-50 border border-emerald-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-3">
            <Target className="w-5 h-5 text-emerald-600" />
            <h3 className="font-semibold text-emerald-900">Current Active Path</h3>
          </div>
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <div className="flex items-center gap-2 mb-2">
                <Activity className="w-4 h-4 text-green-600" />
                <span className="text-sm font-medium text-green-800">
                  Available ({pathSummary.enabledItems.length})
                </span>
              </div>
              {pathSummary.enabledItems.length > 0 ? (
                <ul className="space-y-1">
                  {pathSummary.enabledItems.map((item, index) => (
                    <li key={index} className="text-xs text-green-700 flex items-center gap-1">
                      <CheckCircle2 className="w-3 h-3" />
                      {item.label} ({item.type})
                    </li>
                  ))}
                </ul>
              ) : (
                <div className="text-xs text-gray-500">No actions available</div>
              )}
            </div>
            <div>
              <div className="flex items-center gap-2 mb-2">
                <XCircle className="w-4 h-4 text-red-600" />
                <span className="text-sm font-medium text-red-800">
                  Blocked ({pathSummary.disabledItems.length})
                </span>
              </div>
              {pathSummary.disabledItems.length > 0 ? (
                <ul className="space-y-1">
                  {pathSummary.disabledItems.map((item, index) => (
                    <li key={index} className="text-xs text-red-700 flex items-center gap-1">
                      <XCircle className="w-3 h-3" />
                      {item.label} ({item.type})
                    </li>
                  ))}
                </ul>
              ) : (
                <div className="text-xs text-gray-500">No blocked actions</div>
              )}
            </div>
          </div>
        </div>
      )}
      
      <Accordion type="multiple" defaultValue={currentPage?.navigation?.map((nav, index) => `nav-${index}`)} className="w-full space-y-4">
        {/* Sort navigation items - enabled first */}
        {currentPage?.navigation
          ?.slice()
          ?.sort((a, b) => {
            const aEnabled = isNavigationEnabled(a);
            const bEnabled = isNavigationEnabled(b);
            if (aEnabled && !bEnabled) return -1;
            if (!aEnabled && bEnabled) return 1;
            return 0;
          })
          ?.map((nav: NavigationConfig, index: number) => {
          const isEnabled = isNavigationEnabled(nav);
          
          // Handle Links
          if (nav.type === "link" && nav.navigate) {
            return (
              <AccordionItem key={nav.label} value={`nav-${index}`} className={cn(
                "border-2 rounded-lg",
                isEnabled ? "border-cyan-400 shadow-md" : "border-cyan-200"
              )}>
                <AccordionTrigger className="p-4 hover:no-underline">
                  <div className="flex items-center gap-3 w-full">
                    {isEnabled && <Star className="w-4 h-4 text-cyan-600 animate-pulse" />}
                    <LinkIcon className="w-5 h-5 text-cyan-600" />
                    <div className="flex-1 text-left">
                      <div className={cn(
                        "font-semibold text-cyan-900",
                        isEnabled && "font-bold"
                      )}>
                        {nav.label} {isEnabled && "🎯"}
                      </div>
                      <div className="text-sm text-cyan-700 mt-1">Link Navigation</div>
                    </div>
                    <div className={cn(
                      "px-3 py-1 rounded-full text-xs font-bold",
                      isEnabled 
                        ? "bg-cyan-600 text-white animate-pulse" 
                        : "bg-cyan-200 text-cyan-800"
                    )}>
                      {isEnabled ? "ACTIVE" : "LINK"}
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="p-4 pt-0">
                  <div className="bg-gradient-to-r from-cyan-50 to-blue-50 border border-cyan-200 rounded-lg p-3">
                    <div className="text-sm text-cyan-700 flex items-center gap-1">
                      <ArrowRight className="w-3 h-3" />
                      Navigate to: <code className="bg-cyan-100 px-1 rounded text-xs">{nav.navigate.url}</code>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            );
          }

          // Handle Buttons
          if (nav.type === "button" && nav.action) {
            return (
              <AccordionItem key={nav.label} value={`nav-${index}`} className={cn(
                "border-2 rounded-lg",
                isEnabled ? "border-slate-400 shadow-md" : "border-slate-200 opacity-75"
              )}>
                <AccordionTrigger className="p-4 hover:no-underline">
                  <div className="flex items-center gap-3 w-full">
                    {isEnabled && <Star className="w-4 h-4 text-slate-600 animate-pulse" />}
                    <Mouse className="w-5 h-5 text-slate-600" />
                    <div className="flex-1 text-left">
                      <div className={cn(
                        "font-semibold text-slate-900",
                        isEnabled && "font-bold"
                      )}>
                        {nav.label} {isEnabled && "🎯"}
                      </div>
                      <div className="text-sm text-slate-600 mt-1">Button Action</div>
                    </div>
                    <div className={cn(
                      "px-3 py-1 rounded-full text-xs font-bold",
                      isEnabled 
                        ? "bg-slate-600 text-white animate-pulse" 
                        : "bg-slate-200 text-slate-800"
                    )}>
                      {isEnabled ? "ACTIVE" : "DISABLED"}
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="p-4 pt-0">
                  <div className="space-y-3">
                    {renderAction(nav.action, nav.label)}
                  </div>
                </AccordionContent>
              </AccordionItem>
            );
          }

          return null;
        })}
      </Accordion>
    </div>
  );
};

export default Guards;
