import { cn } from "@/lib/utils";
import React, { useState, useEffect } from "react";
import { Textarea } from "@/components/ui/textarea";
import { useMachineContext } from "../../providers/MachineProvider";
import { useFormContext } from "../../providers/FormProvider";

export default function TextAreaComponent({
  input,
  required,
}: {
  input: any;
  required: boolean;
}) {
  const { updateContextValue, getContextValue } = useMachineContext();
  const { errors } = useFormContext();
  const value = getContextValue(input.id) || "";

  const [debouncedValue, setDebouncedValue] = useState(value);


  useEffect(() => {
    const handler = setTimeout(() => {
      updateContextValue(input.id, debouncedValue);
      console.log("test")

    }, 300); 

    return () => {
      clearTimeout(handler); 
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debouncedValue]);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setDebouncedValue(e.target.value);
  };

  return (
    <div className={cn("", input?.className)}>
      <div className={cn("flex flex-col gap-1")} key={input?.label}>
        <label
          className="font-base text-left text-neutral-600"
          htmlFor={input.id}
        >
          {input?.label}
          {required && <span className="text-red-600">*</span>}
        </label>
        <Textarea
          value={debouncedValue}
          disabled={input?.disabled}
          onChange={handleChange}
          className={cn(
            `w-full rounded-md border border-neutral-300 p-2 ${
              errors[input.id] ? "border-red-500" : ""
            }`
          )}
          name={input?.id}
          placeholder={input?.placeholder}
          id={input?.id}
        />
      </div>
      {input?.id && errors[input.id] && (
        <p className="mb-2 text-sm text-red-500">
          {errors[input.id]?.toString()}
        </p>
      )}
    </div>
  );
}
