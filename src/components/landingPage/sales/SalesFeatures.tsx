import {
  BarChart4,
  Clock,
  Database,
  FileText,
  Shield,
  Users,
  CheckCircle,
  ArrowRight,
} from "lucide-react";
import React, { useState } from "react";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";

export default function SalesFeatures() {
  const [activeFeature, setActiveFeature] = useState(0);
  const { push } = useRouter();

  const features = [
    {
      icon: <FileText className="h-6 w-6 text-white" />,
      title: "Digital Applications",
      description:
        "Replace paper forms with intelligent digital applications that streamline processes and reduce errors.",
      color: "bg-blue-600",
      stats: "93% reduction in manual tasks",
      benefit: "Save valuable time and let residents handle tasks online",
    },
    {
      icon: <Clock className="h-6 w-6 text-white" />,
      title: "Faster Processing",
      description:
        "Accelerate permit approvals with automated workflows and instant notifications.",
      color: "bg-green-600",
      stats: "70% faster permit approvals",
      benefit: "Reduce processing time from weeks to minutes",
    },
    {
      icon: <Database className="h-6 w-6 text-white" />,
      title: "Centralized System",
      description:
        "Manage all types of permits and licenses in one unified platform built for municipalities.",
      color: "bg-purple-600",
      stats: "All municipal services in one place",
      benefit: "Streamline operations and enhance online service access",
    },
    {
      icon: <Users className="h-6 w-6 text-white" />,
      title: "Self-Service Portal",
      description:
        "Provide 24/7 online access for residents to submit, track, and manage permits at their convenience.",
      color: "bg-amber-600",
      stats: "Round-the-clock digital access",
      benefit: "Empower residents and save administrative time",
    },
    {
      icon: <BarChart4 className="h-6 w-6 text-white" />,
      title: "Insightful Analytics",
      description:
        "Gain real-time visibility with robust reporting tools to identify bottlenecks and optimize efficiency.",
      color: "bg-red-600",
      stats: "Instant bottleneck detection",
      benefit: "Make data-driven decisions quickly",
    },
    {
      icon: <Shield className="h-6 w-6 text-white" />,
      title: "Compliance Assurance",
      description:
        "Maintain regulatory compliance with built-in rules and automatic updates for changing standards.",
      color: "bg-indigo-600",
      stats: "Always up-to-date with regulations",
      benefit: "Focus on service delivery while we handle compliance",
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
    },
  };

  return (
    <section
      className="bg-gradient-to-b from-white to-slate-50 py-16 md:py-32"
      id="features"
    >
      <div className="container mx-auto max-w-7xl px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="mb-16 flex flex-col items-center text-center"
        >
          <span className="mb-4 rounded-full bg-blue-100 px-4 py-1 text-sm font-medium text-blue-800">
            POWERFUL FEATURES
          </span>
          <h2 className="bg-gradient-to-r from-blue-800 to-indigo-600 bg-clip-text text-3xl font-bold text-transparent md:text-5xl">
            Transform Your Municipality
          </h2>
          <p className="mx-auto mt-6 max-w-2xl text-xl text-gray-600">
            Join the leading municipalities that are redefining service delivery
            and empowering residents with online convenience.
          </p>
        </motion.div>

        {/* Feature Showcase */}
        <div className="mb-16 grid grid-cols-1 gap-8 lg:grid-cols-2">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="overflow-hidden rounded-xl bg-white shadow-xl"
          >
            <div className={`p-8 ${features[activeFeature].color}`}>
              <div className="mb-4 flex items-center">
                <div className="mr-4 rounded-lg bg-white bg-opacity-20 p-3">
                  {features[activeFeature].icon}
                </div>
                <h3 className="text-2xl font-bold text-white">
                  {features[activeFeature].title}
                </h3>
              </div>
              <p className="mb-4 text-lg text-white">
                {features[activeFeature].description}
              </p>
              <div className="mt-6 rounded-lg bg-white bg-opacity-10 p-4">
                <div className="mb-3 flex items-center">
                  <CheckCircle className="mr-2 h-5 w-5 text-white" />
                  <p className="font-semibold text-white">
                    {features[activeFeature].stats}
                  </p>
                </div>
                <div className="flex items-center">
                  <CheckCircle className="mr-2 h-5 w-5 text-white" />
                  <p className="font-semibold text-white">
                    {features[activeFeature].benefit}
                  </p>
                </div>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="flex flex-col justify-center"
          >
            <h3 className="mb-6 text-2xl font-bold">
              Choose a feature to explore
            </h3>
            <motion.div
              variants={containerVariants}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              className="grid grid-cols-1 gap-4 md:grid-cols-2"
            >
              {features.map((feature, index) => (
                <motion.button
                  key={index}
                  variants={itemVariants}
                  onClick={() => setActiveFeature(index)}
                  className={`flex items-center rounded-lg border p-4 transition-all duration-300 ${
                    activeFeature === index
                      ? `${feature.color} border-transparent text-white`
                      : "border-gray-200 bg-white hover:border-gray-300"
                  }`}
                >
                  <div
                    className={`mr-3 rounded-lg p-2 ${
                      activeFeature === index
                        ? "bg-white bg-opacity-20"
                        : feature.color
                    }`}
                  >
                    {React.cloneElement(feature.icon, {
                      className: `h-5 w-5 ${activeFeature === index ? "text-white" : "text-white"}`,
                    })}
                  </div>
                  <span className="font-medium">{feature.title}</span>
                </motion.button>
              ))}
            </motion.div>
          </motion.div>
        </div>

        {/* Social Proof */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="rounded-xl border border-gray-100 bg-white p-8 shadow-lg"
        >
          <div className="flex flex-col items-center justify-between md:flex-row">
            <div className="mb-6 md:mb-0">
              <h3 className="mb-2 text-xl font-bold">
                Join municipalities saving time and boosting resident
                convenience
              </h3>
              <p className="text-gray-600">
                Experience a faster, smoother service delivery with immediate
                online access
              </p>
            </div>
            <div className="flex flex-col gap-4 md:flex-row">
              <button
                onClick={() => push("/demo")}
                className="flex items-center justify-center rounded-lg bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-3 font-semibold text-white"
              >
                Request a Demo
                <ArrowRight className="ml-2 h-5 w-5" />
              </button>
              <button className="rounded-lg border border-gray-300 px-6 py-3 font-semibold text-gray-700">
                View Case Studies (Coming Soon)
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
} 