"use client";

import React, { useEffect, useState } from "react";
import { AnimatePresence, motion } from "framer-motion";
import Link from "next/link";
import Image from "next/image";
import { Menu, X } from "lucide-react";
import { usePathname } from "next/navigation";
import { useActiveSection } from "@/hooks/useActiveSection";

import {
  NavigationMenu,
  NavigationMenuList,
  NavigationMenuItem,
  NavigationMenuTrigger,
  NavigationMenuContent,
  NavigationMenuLink,
} from "@/components/ui/navigation-menu";
import { Button } from "@/components/ui/button";

export default function SalesNavbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const pathname = usePathname();
  const activeSection = useActiveSection();

  useEffect(() => {
    if (isMenuOpen) {
      const scrollY = window.scrollY;
      document.body.style.overflow = "hidden";
      document.body.style.position = "fixed";
      document.body.style.top = `-${scrollY}px`;
      document.body.style.width = "100%";
    } else {
      document.body.style.overflow = "";
      document.body.style.position = "";
      document.body.style.top = "";
      document.body.style.width = "";
    }
  }, [isMenuOpen]);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  function linkIsActive(linkHref: string): boolean {
    const [pathPart, hashPart] = linkHref.split("#");
    const routeToCompare = pathPart === "" ? "/sales" : pathPart;

    if (routeToCompare !== pathname) {
      return false;
    }

    if (hashPart) {
      return activeSection === hashPart;
    }

    if (linkHref === "/sales") {
      return activeSection === "" || activeSection === "home";
    }

    return activeSection === "";
  }

  const navLinks = [
    { name: "Overview", href: "/sales" },
    { name: "Features", href: "/sales#features" },
    { name: "Solutions", href: "/sales#solutions", dropdown: true },
    { name: "About", href: "/sales#about" },
  ];

  const solutionsItems = [
    {
      title: "Platform Overview",
      href: "/sales#solutions",
      description: "Explore all our solutions",
    },
    {
      title: "Request Demo",
      href: "/demo",
      description: "See our product in action",
    },
    {
      title: "Case Studies (Soon)",
      href: "#",
      disabled: true,
      description: "Learn from our customers",
    },
  ];

  return (
    <header className="fixed top-0 left-0 right-0 z-50 w-full border-b border-gray-200 bg-white/95 backdrop-blur-sm ">
      {/* Municipal focus banner */}
      <div className="bg-blue-600 text-white py-1 text-center text-xs sm:text-sm">
        <span className="font-medium">Municipal Solutions</span> • Built for government efficiency
        <Link href="/" className="ml-4 underline hover:no-underline">
          Back to Main Site →
        </Link>
      </div>
      
      <div className="container mx-auto flex h-16 items-center justify-between px-6 max-w-8xl w-full">
        {/* -- Logo -- */}
        <Link href="/sales" className="flex items-center gap-2">
          <Image
            src="/logos/ClerkXpress.svg"
            alt="ClerkXpress Logo"
            width={40}
            height={40}
          />
          <div>
            <span className="text-xl font-semibold md:text-2xl">CLERK</span>
            <span className="text-xl font-light text-clerk-background md:text-2xl">
              XPRESS
            </span>
            <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
              Municipal
            </span>
          </div>
        </Link>

        {/* -- Desktop Navigation -- */}
        <div className="hidden md:flex">
          <NavigationMenu>
            <NavigationMenuList className="flex items-center gap-6">
              {navLinks.map((link) => {
                if (link.dropdown) {
                  const isActive = linkIsActive(link.href);
                  return (
                    <NavigationMenuItem key={link.name}>
                      <NavigationMenuTrigger
                        onClick={(e) => {
                          e.preventDefault();
                        }}
                        className={`text-sm font-medium ${
                          isActive ? "text-blue-600" : "text-gray-700"
                        }`}
                      >
                        {link.name}
                      </NavigationMenuTrigger>
                      <NavigationMenuContent>
                        <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-1 lg:grid-cols-2">
                          {solutionsItems.map((item) => {
                            const itemIsActive = linkIsActive(item.href);
                            return (
                              <li key={item.title} className="row-span-3">
                                <NavigationMenuLink asChild className="group">
                                  {item.disabled ? (
                                    <div className="cursor-not-allowed select-none rounded-md bg-gradient-to-b from-white to-gray-50 p-4 opacity-70 hover:from-red-100 hover:to-red-200">
                                      <div className="mb-2 mt-4 font-medium text-gray-900">
                                        {item.title}
                                      </div>
                                      <p className="text-sm text-gray-600">
                                        {item.description}
                                      </p>
                                    </div>
                                  ) : (
                                    <Link
                                      href={item.href}
                                      className={`flex h-full w-full select-none flex-col justify-end rounded-md bg-gradient-to-b from-white to-gray-50 p-4 no-underline outline-none hover:from-blue-50 hover:to-blue-100 focus:shadow-md ${
                                        itemIsActive ? "text-blue-700" : ""
                                      }`}
                                    >
                                      <div className="mb-2 mt-4 font-medium text-gray-900">
                                        {item.title}
                                      </div>
                                      <p className="text-sm text-gray-600">
                                        {item.description}
                                      </p>
                                    </Link>
                                  )}
                                </NavigationMenuLink>
                              </li>
                            );
                          })}
                        </ul>
                      </NavigationMenuContent>
                    </NavigationMenuItem>
                  );
                } else {
                  // Normal link (Overview / Features / About)
                  const isActive = linkIsActive(link.href);
                  return (
                    <NavigationMenuItem key={link.name}>
                      <Link
                        href={link.href}
                        className={`relative text-sm font-medium transition duration-150 hover:text-blue-600 ${
                          isActive ? "text-blue-600" : "text-gray-700"
                        }`}
                      >
                        {link.name}
                        {isActive && (
                          <motion.div
                            layoutId="activeLink"
                            className="absolute -bottom-1 left-0 h-0.5 w-full bg-blue-600"
                            transition={{
                              type: "spring",
                              stiffness: 380,
                              damping: 30,
                            }}
                          />
                        )}
                      </Link>
                    </NavigationMenuItem>
                  );
                }
              })}
            </NavigationMenuList>
          </NavigationMenu>
        </div>

        {/* -- Desktop CTA Buttons -- */}
        <div className="hidden items-center space-x-3 md:flex gap-2">
          <Link
            href="/"
            className="text-sm font-medium text-gray-700 hover:text-blue-600"
          >
            Resident Portal
          </Link>
          <Link
            href="/demo"
            className="rounded bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
          >
            Request Demo
          </Link>
        </div>

        {/* -- Mobile Menu Button -- */}
        <button
          className="rounded-md p-2 text-gray-600 focus:outline-none md:hidden"
          onClick={toggleMenu}
          aria-label="Toggle navigation menu"
        >
          {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
        </button>
      </div>

      {/* -- Mobile Menu -- */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden border-b border-gray-200 bg-white md:hidden"
          >
            <div className="container mx-auto px-4 py-4">
              <nav className="flex flex-col space-y-4">
                {navLinks.map((link) => {
                  if (link.dropdown) {
                    const isActive = linkIsActive(link.href);
                    return (
                      <div key={link.name} className="py-2">
                        <div
                          className={`flex items-center justify-between py-2 text-base font-medium ${
                            isActive ? "text-blue-600" : "text-gray-700"
                          }`}
                        >
                          {link.name}
                        </div>
                        <div className="ml-4 mt-2 flex flex-col space-y-2">
                          {solutionsItems.map((item) => {
                            const itemIsActive = linkIsActive(item.href);
                            return item.disabled ? (
                              <div
                                key={item.title}
                                className="cursor-not-allowed py-1 text-sm text-gray-400"
                              >
                                {item.title}
                              </div>
                            ) : (
                              <Link
                                key={item.title}
                                href={item.href}
                                className={`py-1 text-sm hover:text-blue-600 ${
                                  itemIsActive ? "text-blue-600" : "text-gray-600"
                                }`}
                                onClick={() => setIsMenuOpen(false)}
                              >
                                {item.title}
                              </Link>
                            );
                          })}
                        </div>
                      </div>
                    );
                  } else {
                    const isActive = linkIsActive(link.href);
                    return (
                      <Link
                        key={link.name}
                        href={link.href}
                        className={`relative py-2 text-base font-medium transition-colors duration-200 ${
                          isActive
                            ? "text-blue-600"
                            : "text-gray-700 hover:text-blue-600"
                        }`}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {link.name}
                        {isActive && (
                          <motion.div
                            initial={{ scale: 0, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            className="absolute right-0 h-2 w-2 rounded-full bg-blue-600"
                          />
                        )}
                      </Link>
                    );
                  }
                })}

                <div className="mt-4 flex flex-col space-y-3 pt-4">
                  <Link
                    href="/"
                    className="py-2 text-center text-base font-medium text-gray-700 hover:text-blue-600"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Back to Main Site →
                  </Link>
                  <Button
                    className="w-full bg-blue-600 hover:bg-blue-700"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Request Demo
                  </Button>
                </div>
              </nav>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  );
} 