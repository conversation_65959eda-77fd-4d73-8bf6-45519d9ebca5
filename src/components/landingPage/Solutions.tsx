import React, { useState } from "react";
import { motion } from "framer-motion";
import { FileText, Users, Layers, Settings, Clock } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton"; // <-- Import Shadcn skeleton
import Image from "next/image";
import { useRouter } from "next/navigation";

const Solutions = () => {
  const [activeTab, setActiveTab] = useState("licensing");
  const [isImageLoading, setIsImageLoading] = useState(true);

  const solutions = [
    {
      id: "licensing",
      title: "Licensing & Permits",
      icon: <FileText className="h-10 w-10 text-blue-600" />,
      description:
        "Simplify your permit processes with digital applications that cut down on wait times and streamline approvals.",
      features: [
        "Online permit submissions with real-time tracking",
        "Automated review and renewals",
        "Customizable forms for every permit type",
        "Integrated payment and verification",
      ],
      image: "/images/screenshot2.png",
    },
    {
      id: "records",
      title: "Vital Records",
      icon: <FileText className="h-10 w-10 text-blue-600" />,
      description:
        "Manage and issue vital records securely, ensuring residents get access when they need it—anytime, anywhere.",
      features: [
        "Effortless birth certificate processing",
        "Online marriage license applications",
        "Streamlined death certificate management",
        "Secure digital delivery",
      ],
      image: "/images/vital.jpg",
    },
    {
      id: "admin",
      title: "Administrative Tools",
      icon: <Settings className="h-10 w-10 text-blue-600" />,
      description:
        "Boost back-office efficiency with tools that automate tasks and reduce manual data entry.",
      features: [
        "Automated review workflows",
        "Digital document management",
        "Real-time revenue and operations tracking",
        "Customizable approval processes",
      ],
      image: "/images/backoffice.png",
    },
    {
      id: "citizen",
      title: "Citizen Portal",
      icon: <Users className="h-10 w-10 text-blue-600" />,
      description:
        "Empower residents with a self-service portal that provides 24/7 access to municipal services.",
      features: [
        "Instant status updates on applications",
        "Secure resident account management",
        "Automated notifications and reminders",
        "Easy document uploads and digital submissions",
      ],
      image: "/images/screenshot1.png",
    },
    {
      id: "convenience",
      title: "Digital Convenience",
      icon: <Clock className="h-10 w-10 text-blue-600" />,
      description:
        "Experience a modern municipal service that prioritizes speed and accessibility, letting residents take control online.",
      features: [
        "Fast, online service delivery",
        "Automated processing to save time",
        "Unified access across departments",
        "Real-time communication and support",
      ],
      image: "/images/digital-convenience.jpg",
    },
    {
      id: "integration",
      title: "Integration Suite",
      icon: <Layers className="h-10 w-10 text-blue-600" />,
      description:
        "Seamlessly connect existing municipal systems with our integrated platform for a smooth operational experience.",
      features: [
        "API access for custom integrations",
        "GIS and mapping system connectivity",
        "Payment gateway and document management",
        "Real-time data synchronization",
      ],
      image: "/images/loginBackground.png",
    },
  ];

  const activeSolution =
    solutions.find((s) => s.id === activeTab) || solutions[0];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 300, damping: 24 },
    },
  };

  const { push } = useRouter();

  return (
    <section id="solutions">
      <div className="bg-gray-50 py-20">
        <div className="container mx-auto max-w-5xl px-6">
          {/* Section Header */}
          <div className="mb-12 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              <span className="rounded-full bg-blue-600 px-4 py-1 text-sm font-medium text-white">
                SOLUTIONS
              </span>
            </motion.div>
            <motion.h2
              className="mt-6 text-4xl font-bold md:text-5xl"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              Modernize Your Municipal Services
            </motion.h2>
            <motion.p
              className="mx-auto mt-4 max-w-2xl text-lg text-gray-600"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              Our solutions are designed to save time, streamline operations, and empower residents with 24/7 online access.
            </motion.p>
          </div>

          {/* Solution Tabs */}
          <div className="mb-10 flex flex-wrap justify-center gap-2">
            {solutions.map((solution) => (
              <motion.button
                key={solution.id}
                className={`rounded-full px-4 py-2 text-sm font-medium transition-all duration-200 md:text-base ${
                  activeTab === solution.id
                    ? "bg-blue-600 text-white"
                    : "bg-white text-gray-700 hover:bg-gray-100"
                }`}
                onClick={() => {
                  setActiveTab(solution.id);
                  // reset loading state when switching tabs
                  setIsImageLoading(true);
                }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {solution.title}
              </motion.button>
            ))}
          </div>

          {/* Solution Content */}
          <div className="mt-16 grid gap-8 md:grid-cols-2 md:gap-12 lg:mt-20">
            {/* Solution Details */}
            <motion.div
              key={activeSolution.id + "-content"}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.5 }}
              className="flex flex-col justify-center"
            >
              <div className="mb-6 flex items-center">
                <div className="mr-4 rounded-lg bg-blue-100 p-3">
                  {activeSolution.icon}
                </div>
                <h3 className="text-2xl font-bold md:text-3xl">
                  {activeSolution.title}
                </h3>
              </div>

              <p className="mb-6 text-lg text-gray-600">
                {activeSolution.description}
              </p>

              <motion.ul
                className="mb-8 space-y-3"
                variants={containerVariants}
                initial="hidden"
                animate="visible"
              >
                {activeSolution.features.map((feature, index) => (
                  <motion.li
                    key={index}
                    className="flex items-start"
                    variants={itemVariants}
                  >
                    <div className="mr-3 mt-1 flex-shrink-0 text-blue-600">
                      <svg
                        className="h-5 w-5"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <span>{feature}</span>
                  </motion.li>
                ))}
              </motion.ul>

              <Button
                onClick={() => {
                  push("/demo");
                }}
                className="w-fit bg-blue-600 hover:bg-blue-700"
              >
                Learn more about {activeSolution.title}
              </Button>
            </motion.div>

            {/* Solution Image */}
            <motion.div
              key={activeSolution.id + "-image"}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.5 }}
              className="relative flex items-center justify-center"
            >
              <div className="overflow-hidden rounded-xl bg-white p-2 shadow-xl flex justify-center items-center relative">
                {/* Skeleton (shows while image is loading) */}
                {isImageLoading && (
                  <Skeleton className="absolute inset-0 h-full w-full rounded-lg" />
                )}

                <Image
                  src={activeSolution.image}
                  alt={`${activeSolution.title} screenshot`}
                  width={800}
                  height={500}
                  className={`rounded-lg object-cover transition-opacity duration-500 ${
                    isImageLoading ? "opacity-0" : "opacity-100"
                  }`}
                  loading="lazy"
                  onLoadingComplete={() => setIsImageLoading(false)}
                />
              </div>

              {/* Decorative Elements */}
              <motion.div
                className="absolute -right-4 -top-4 h-24 w-24 rounded-full bg-blue-100 md:-right-8 md:-top-8"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.3, duration: 0.5 }}
              />
              <motion.div
                className="absolute -bottom-4 -left-4 h-16 w-16 rounded-full bg-blue-200 md:-bottom-8 md:-left-8"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.4, duration: 0.5 }}
              />
            </motion.div>
          </div>
        </div>
      </div>
      <div className="bg-blue-600 py-16 text-white" id="contact-us">
        <div className="container mx-auto max-w-5xl px-6 text-center">
          <h2 className="mb-6 text-3xl font-bold">
            Ready to streamline your municipal services?
          </h2>
          <p className="mx-auto mb-8 max-w-xl text-blue-100">
            Join numerous municipalities that have embraced digital solutions to save time and offer unmatched online convenience.
          </p>
          <Button
            onClick={() => {
              push("/demo");
            }}
            size="lg"
            className="bg-white text-blue-600 hover:bg-blue-50"
          >
            Request a demo
          </Button>
        </div>
      </div>
    </section>
  );
};

export default Solutions;
