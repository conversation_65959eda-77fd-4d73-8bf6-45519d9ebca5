[{"id": "dashboard", "href": "/dashboard", "icon": "layout-dashboard", "label": "Dashboard", "parentId": null, "permissions": ["super-admin"]}, {"id": "accountManagement", "href": null, "icon": "users", "label": "Account Management", "groups": [{"id": "resident", "label": "Resident", "links": [{"id": "newResident", "href": "/onlineForm/new-resident-registration-clerk", "icon": "plus", "label": "New Account", "parentId": "accountManagement", "permissions": ["super-admin"]}, {"id": "residentAccountDirectory", "href": "/resident/search", "icon": "user", "label": "Account Directory", "parentId": "accountManagement", "permissions": ["super-admin"]}, {"id": "mergeAccounts", "href": "/approval/accounts/merge", "icon": "merge", "label": "<PERSON><PERSON> Accounts", "parentId": "activities", "permissions": ["super-admin"]}]}], "parentId": null, "permissions": ["super-admin"]}, {"id": "licensesAdminPermits", "href": null, "icon": "scroll-text", "label": "Licenses & Permits", "groups": [{"id": "licensesAdmin", "label": "Licenses", "links": [{"id": "dogLicenseApprovals", "href": "/approval/license/dog", "icon": "clipboard-signature", "label": "Pending Approvals", "parentId": "licensesAdmin", "permissions": ["super-admin"]}]}], "parentId": null, "permissions": ["super-admin"]}, {"id": "animalControl", "href": null, "icon": "paw-print", "label": "Animal Control", "groups": [{"id": "impoundedDogs", "label": "Animal Control", "links": [{"id": "licenseDelinquencies", "href": "/licenses/delinquencies", "icon": "file-clock", "label": "License Delinquencies", "parentId": "animalControl", "permissions": ["super-admin"]}, {"id": "impoundedAnimals", "href": "/animalControl/impoundedAnimals", "icon": "paw-print", "label": "Impounded Dogs", "parentId": "animalControl", "permissions": ["super-admin"]}]}], "parentId": null, "permissions": ["super-admin"]}, {"id": "home", "href": "/home", "icon": "FiHome", "label": "Home", "parentId": null, "permissions": ["resident"]}, {"id": "officeActivities", "href": null, "icon": "HiOutlineOfficeBuilding", "label": "Office Activities", "groups": [{"id": "clerkOperations", "label": "Clerk Operations", "links": [{"id": "<PERSON><PERSON><PERSON><PERSON>", "href": "/reports/clerkFormsAndReports", "icon": "TbReportAnalytics", "label": "Clerk Reports", "parentId": "activities", "permissions": ["super-admin"]}]}], "parentId": null, "permissions": ["super-admin"]}, {"id": "licenses", "href": "/licenses", "icon": "BsFiles", "label": "Licenses", "alerts": ["licenseExpired"], "parentId": null, "permissions": ["resident"]}, {"id": "customerService", "href": null, "icon": "BiSupport", "label": "Customer Service", "groups": [{"id": "support", "label": "Support", "links": [{"id": "contactUs", "href": "/customerService/contactUs", "icon": "BiSupport", "label": "Contact Us", "parentId": "customerService", "permissions": ["resident"]}, {"id": "faqs", "href": "/customerService/faqs", "icon": "FiHelpCircle", "label": "FAQs", "parentId": "customerService", "ariaLabel": "Frequently Asked Questions", "permissions": ["resident"]}]}], "parentId": null, "permissions": ["resident"]}, {"id": "store", "href": null, "icon": "store", "label": "Store", "groups": [{"id": "storeCategories", "label": "Categories", "links": [{"id": "replacements", "href": "/store/replacements", "icon": "BiStore", "label": "Replacements", "parentId": "store", "permissions": ["super-admin", "resident"]}]}], "parentId": null, "permissions": ["super-admin"]}]