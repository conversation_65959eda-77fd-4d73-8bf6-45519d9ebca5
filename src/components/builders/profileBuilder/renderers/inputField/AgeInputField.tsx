import React, { useState, useEffect, useRef } from "react";
import { cn } from "@/lib/utils";
import { format, subMonths, subYears } from "date-fns";

interface AgeData {
  years: number;
  months: number;
}

function calculateBirthDate(ageData: AgeData): string {
  const now = new Date();
  
  let birthDate = new Date(now.getFullYear(), now.getMonth(), 1);
  
  birthDate = subYears(birthDate, ageData.years);
  birthDate = subMonths(birthDate, ageData.months);
  
  return format(birthDate, "yyyy-MM-dd");
}

function calculateAgeFromBirthDate(birthDateString: string): AgeData {
  if (!birthDateString) return { years: 0, months: 0 };
  
  const birthDate = new Date(birthDateString + 'T00:00:00');
  const now = new Date();
  
  if (birthDate > now) {
    return { years: 0, months: 0 };
  }
  
  let totalMonths = (now.getFullYear() - birthDate.getFullYear()) * 12 + (now.getMonth() - birthDate.getMonth());
  
  if (now.getDate() < birthDate.getDate()) {
    totalMonths--;
  }
  
  const years = Math.floor(Math.max(0, totalMonths) / 12);
  const months = Math.max(0, totalMonths) % 12;
  
  // capping at 32 years max but need to adjust this later to allow user to type in amount of years -- Sean B
  return { 
    years: Math.min(years, 32), 
    months: months 
  };
}

function formatAgeDisplay(ageData: AgeData): string {
  const parts = [];
  
  if (ageData.years > 0) {
    parts.push(`${ageData.years} ${ageData.years === 1 ? 'year' : 'years'}`);
  }
  
  if (ageData.months > 0) {
    parts.push(`${ageData.months} ${ageData.months === 1 ? 'month' : 'months'}`);
  }
  
  return parts.join(' ') || '0 years';
}

const AgeInputField: React.FC<InputFieldProps> = ({
  field,
  tempValue,
  handleChange,
  required,
  setTempValue,
  isDisplay = true,
}) => {
  const yearsRef = useRef<HTMLSelectElement>(null);
  const [years, setYears] = useState<number>(0);
  const [months, setMonths] = useState<number>(0);
  const [isInitializing, setIsInitializing] = useState(true);

  useEffect(() => {
    if (tempValue) {
      const ageData = calculateAgeFromBirthDate(tempValue);
      setYears(Math.min(ageData.years, 30));
      setMonths(Math.min(ageData.months, 11));
    } else {
      setYears(0);
      setMonths(0);
    }
    setIsInitializing(false);
  }, [tempValue]);

  useEffect(() => {
    if (!isDisplay && yearsRef.current) {
      yearsRef.current.focus();
    }
  }, [isDisplay]);

  const updateBirthDate = (newYears: number, newMonths: number) => {
    if (newYears > 0 || newMonths > 0) {
      const birthDate = calculateBirthDate({ years: newYears, months: newMonths });
      handleChange(birthDate);
    } else {
      handleChange("");
    }
  };

  const handleYearsChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newYears = parseInt(e.target.value) || 0;
    setYears(newYears);
    if (!isInitializing) {
      updateBirthDate(newYears, months);
    }
  };

  const handleMonthsChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newMonths = parseInt(e.target.value) || 0;
    setMonths(newMonths);
    if (!isInitializing) {
      updateBirthDate(years, newMonths);
    }
  };

  const yearOptions = Array.from({ length: 31 }, (_, i) => i);
  const monthOptions = Array.from({ length: 12 }, (_, i) => i);

  const displayValue = tempValue ? formatAgeDisplay(calculateAgeFromBirthDate(tempValue)) : '';

  return (
    <div className="w-full min-w-0">
      {isDisplay ? (
        <div className="w-full min-w-0">
          <span 
            className="block w-full text-left py-1 text-xs sm:text-xs md:text-base text-gray-700 truncate whitespace-nowrap overflow-hidden"
            title={displayValue}
          >
            {displayValue}
          </span>
        </div>
      ) : (
        <div className="w-full min-w-0">
          <div className="flex gap-2">
            <div className="flex-1">
              <select
                ref={yearsRef}
                value={years}
                onChange={handleYearsChange}
                className={cn(
                  "w-full rounded-md border border-input bg-background px-3 py-2 text-xs ring-offset-background",
                  "file:border-0 file:bg-transparent file:text-sm file:font-medium",
                  "placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2",
                  "focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                )}
              >
                {yearOptions.map((year) => (
                  <option key={year} value={year}>
                    {year} {year === 1 ? 'year' : 'years'}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex-1">
              <select
                value={months}
                onChange={handleMonthsChange}
                className={cn(
                  "w-full rounded-md border border-input bg-background px-3 py-2 text-xs ring-offset-background",
                  "file:border-0 file:bg-transparent file:text-sm file:font-medium",
                  "placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2",
                  "focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                )}
              >
                {monthOptions.map((month) => (
                  <option key={month} value={month}>
                    {month} {month === 1 ? 'month' : 'months'}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AgeInputField; 