import Accordion from "../ui/Accordion2";
import { User } from "@/types/UserType";
import { format } from 'date-fns';

interface ResidencyProps {
  residency: any
}

const calculateYearsAndMonths = (startDate:Date, endDate:Date) => {
  let years = endDate.getFullYear() - startDate.getFullYear();
  let months = endDate.getMonth() - startDate.getMonth();
  
  if (months < 0) {
    years--;
    months += 12;
  }

  return `${years} years, ${months} months`;
}


const ResidencyResults = ({ residency }:ResidencyProps) => {
  const status = residency?.status ?? '';
  const since = residency?.since;
  const reason = residency?.reason ?? '';
  const sinceDate = new Date(since);
  
  const formattedSinceDate = format(sinceDate, 'MMM do, yyyy');
  
  const currentDate = new Date();
  const residencyDuration = calculateYearsAndMonths(sinceDate, currentDate);

  return (
    <Accordion title='Residency'>
      <div className='grid grid-cols-[120px_1fr] text-sm'>
        <p>Status</p>
        <p className={`font-semibold text-neutral-800 ${status === 'Resident' && '!text-green-600'}`}>{status}</p>
        <p>Type</p>
        <p className='font-semibold text-neutral-800'>{reason}</p>
        <p>Since</p>
        <p className='font-semibold text-neutral-800'>{formattedSinceDate}</p>
        <p>Length</p>
        <p className='font-semibold text-neutral-800'>{residencyDuration}</p>
      </div>
    </Accordion>
  )
}

export default ResidencyResults