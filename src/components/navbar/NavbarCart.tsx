"use client";

import { useState, MouseEvent as ReactMouseEvent } from "react";
import { useRouter } from "next/navigation";
import { useParams } from "next/navigation";
import { usePathname } from "next/navigation";
import { FiShoppingCart } from "react-icons/fi";
import Modal from "../modal/Modal";
import Button from "../ui/buttons/Button";
import LoadingSpinner from "../ui/LoadingSpinner";
import NavbarCartItems from "./cart/NavbarCartItems";
import { useMyCart } from "@/hooks/useMyCart";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/Tooltip";
import { useAtom } from "jotai";
import { toastAtom } from "../ui/toast/toast";

const NavbarCart = () => {
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const router = useRouter();
  const pathname = usePathname();
  const { cartSummary, cartLoading, cartError } = useMyCart();
  const [, setToast] = useAtom(toastAtom);

  let cartCount = cartSummary?.items?.length ?? 0;
  if (cartCount > 99) cartCount = 99;

  return (
    <>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger>
            <div
              className="relative flex items-center justify-center"
              onClick={() =>
                cartError
                  ? setToast
                  : cartLoading
                  ? setIsModalOpen(true)
                  : setIsModalOpen(true)
              }
            >
              <FiShoppingCart className="text-xl text-neutral-500" />
              {cartCount > 0 && (
                <div className="absolute -top-2 -right-2 w-fit px-1.5 bg-red-600 rounded-full text-white flex items-center justify-center font-semibold border border-white">
                  <span className="sr-only">Cart Item Count</span>
                  <span className="text-xs">{cartCount}</span>
                </div>
              )}
              {cartError && (
                <div className="absolute -top-2 -right-2 w-fit px-1.5 bg-[#ed115D] rounded-full text-white flex items-center justify-center font-semibold border border-white">
                  <span className="sr-only">Cart Item Count</span>
                  <span className="text-xs">!</span>
                </div>
              )}
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>Cart</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="Cart"
        position="right"
      >
        <div className="w-full max-w-smbn z-10 flex flex-col overflow-hidden h-full gap-4">
          <div className="flex flex-col max-h-full overflow-auto">
            {cartLoading && cartSummary && (
              <div>
                <LoadingSpinner />{" "}
              </div>
            )}

            {cartSummary && cartCount > 0 ? (
              <NavbarCartItems
                items={cartSummary?.items ?? []}
                cartId={cartSummary?.cartId ?? ""}
                setIsModalOpen={setIsModalOpen}
              />
            ) : (
              <>
                <div className="flex items-center justify-center">
                  <FiShoppingCart className="text-4xl text-neutral-500" />
                </div>
                <div className="text-center">Your cart is empty</div>
              </>
            )}
          </div>

          <div className="">
            <Button
              className="w-full"
              variant="primary"
              onClick={() => {
                if (pathname !== `/cart`)
                  router.push(`/cart`);
                setIsModalOpen(false);
              }}
            >
              Go to Cart
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default NavbarCart;
