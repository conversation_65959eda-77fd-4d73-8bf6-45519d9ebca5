import LoadingSpinner from "@/components/ui/LoadingSpinner";
import {
  useGetLicenseFeeTable,
  useGetLicenseFees,
  useGetResidentLicenseFeeTable,
  useGetResidentLicenseFees,
} from "@/hooks/api/useLicense";
import { AnimatePresence, motion } from "framer-motion";
import { useSearchParams } from "next/navigation";
import FeesSection from "@/components/fees/FeesSection";
import { useMyProfile } from "@/hooks/providers/useMyProfile";

export const FeesTab = ({ licenseId }: { licenseId: string }) => {
  
  const { hasPermissions } = useMyProfile();
  const admin = hasPermissions(["super-admin"]);

  const {
    data: clerkData,
    isLoading: clerkIsLoading,
    isError: clerkIsError,
    error: clerkError,
  } = useGetLicenseFees(licenseId);
  const {
    data: residentData,
    isLoading: residentIsLoading,
    isError: residentIsError,
    error: residentError,
  } = useGetResidentLicenseFees(licenseId);

  const {
    data: clerkFeeTable,
    isLoading: clerkFeeTableLoading,
    isError: clerkFeeTableError,
  } = useGetLicenseFeeTable();
  const {
    data: residentFeeTable,
    isLoading: residentFeeTableLoading,
    isError: residentFeeTableError,
  } = useGetResidentLicenseFeeTable();

  const data = admin ? clerkData : residentData;
  const feeTable = admin ? clerkFeeTable : residentFeeTable;
  const isLoading = admin ? clerkIsLoading : residentIsLoading;
  const isError = admin ? clerkIsError : residentIsError;
  const error = admin ? clerkError : residentError;
  const feeTableLoading = admin
    ? clerkFeeTableLoading
    : residentFeeTableLoading;
  const feeTableError = admin ? clerkFeeTableError : residentFeeTableError;

  const searchParams = useSearchParams();
  const selectionId = searchParams.get("licenseId");

  const containerVariants = {
    hidden: { opacity: 0, y: -50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { type: "spring", stiffness: 100 },
    },
    exit: { opacity: 0, y: 50, transition: { duration: 0.2 } },
  };

  if (isLoading && feeTableLoading) return <LoadingSpinner />;
  if (isError) {
    console.log(error);
    return <div>Failed to load user data</div>;
  }

  if (feeTableError) {
    console.log(feeTableError);
    return <div>Failed to load fee table data</div>;
  }

  if (data && feeTable) {
    return (
      <>
        <AnimatePresence mode="wait">
          <motion.div
            key={selectionId}
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="flex w-full flex-col gap-10 rounded bg-white p-6 shadow"
          >
            {data?.items?.map((fee: any, index: number) => {
              return <FeesSection fees={fee} key={"fee" + index} />;
            })}
          </motion.div>
        </AnimatePresence>
      </>
    );
  }

  return null;
};
