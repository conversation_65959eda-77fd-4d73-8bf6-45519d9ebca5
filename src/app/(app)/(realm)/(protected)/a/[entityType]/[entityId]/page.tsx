"use client";
import { useRouter, useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import RenewSelect from "./RenewSelect";
import DownloadLicenseButton2 from "./DownloadLicenseButton2";
import { useDeleteLicense2 } from "@/hooks/api/useLicense";
import { useMyCart } from "@/hooks/useMyCart";
import { useQueryClient } from "@tanstack/react-query";
import { toastAtom } from "@/components/ui/toast/toast";
import { useAtom } from "jotai";
import { DogsContent } from "./DogTab";
import { isSenior } from "@/components/license/licenseHelper";
import ApplicationHeader from "./ApplicationHeader";
import { useState } from "react";
import { OwnersContent } from "./OwnerTab";
import { FiShoppingCart } from "react-icons/fi";
import { LuRedo2, LuTrash, LuUndo } from "react-icons/lu";
import { License } from "@/types/LicenseType";
import { FeesTab } from "@/components/fees/FeesTab";
import { EntityFiles } from "@/components/files/EntityFiles";
import { FaTag } from "react-icons/fa";
import { CgSpinner } from "react-icons/cg";
import NewTicket from "@/components/navbar/menu/NewTicket";
import Modal from "@/components/modal/Modal";
import { BiSupport } from "react-icons/bi";
import { userAtom } from "@/components/sidebar/main/User";
import { useMyProfile } from "@/hooks/providers/useMyProfile";
import { useEntity } from "@/hooks/providers/useEntity";
import { EntityHistory } from "@/components/history/EntityHistory";
import { useResubmitLicense } from "@/hooks/api/useResident";
import LicenseContent from "./LicenseContent";
import ApplicationTabs from "./ApplicationTabs";
import { useGetLicenseFeesCalculation } from "@/hooks/api/useLicense";
import { useUpdateEntityRejectedFields } from "@/hooks/api/useApprovals";
import { useGetRedmineProjectId } from "@/hooks/api/useSupport";

export default function LicensePage() {
  const searchParams = useSearchParams();
  const tab = searchParams.get("tab");
  const { addToCart } = useMyCart();
  const { hasPermissions } = useMyProfile();
  const permitted = hasPermissions(["super-admin"]);
  const { entity, entityId, entityType, entityRefetch, entityIsFetching } =
    useEntity();

  const currentEntity = entity[entityType];
  const individual = entity["individual"] ?? null;
  const dogs = entity["dog"] ?? null;
  const dog = dogs?.length ? dogs[0] : null;
  const actions = currentEntity?.actions ?? [];
  const status = currentEntity?.status ?? null;
  const pendingPayment =
    (actions.includes("pendingPayment") || status === "Pending Payment") ??
    null;

  const pendingApproval =
    (actions.includes("pendingApproval") || status === "Pending Approval") ??
    null;


  const isPendingLicense =
    currentEntity?.licenseNumber.includes("PEND") || false;

  // Check if renewal is actually possible (same logic as RenewSelect)
  const { data: renewalData } = useGetLicenseFeesCalculation(entityId);
  const canActuallyRenew =
    renewalData && renewalData.duration > 0 && renewalData.duration !== null;

  // Discounts ---------------------
  const discounts: any[] = [];

  if (isSenior(individual?.dateOfBirth)) {
    discounts.push("senior");
  }

  if (dog?.licenseExempt) {
    discounts.push("serviceDog");
  }

  if (dog?.dogSpayedOrNeutered === "yes") {
    discounts.push("spayNeuter");
  }

  const isDog = () => {
    switch (entityType) {
      case "dog":
        return entity[entityType].active;
      case "license":
        return entity[entityType].licenseType.groupName === "Dog";
      default:
        return false;
    }
  };

  const renewalTable: { [key: string]: string } = {
    pendingPayment: "Pending Payment",
    pendingApproval: "Pending Approval",
    canRenew: "Can Renew",
    canDelete: "Can Delete",
    canCancelRenewal: "Can Cancel Renewal",
    canAddToCart: "Can Add to Cart",
    canDownload: "Can Download",
  };

  return (
    <div className="h-full gap-6 overflow-y-auto py-10">
      {/* Header */}
      <ApplicationHeader application={currentEntity} />
      {/* Content */}{" "}
      <div className="flex w-full shrink-0 flex-col gap-10 px-2 py-6 lg:flex-row lg:p-6">
        {/* Tabs */}
        <div className="flex h-fit w-full flex-col gap-6 overflow-x-hidden">
          <ApplicationTabs entity={entity} />
          <div className="flex w-full gap-2">
            {(() => {
              const tabContent: {
                [key: string]: any;
              } = {
                license: (
                  <LicenseContent license={currentEntity} key={entityId} />
                ),
                dog: (
                  <DogsContent
                    dogs={dogs}
                    license={currentEntity}
                    key={entityId}
                  />
                ),
                individual: (
                  <OwnersContent
                    owner={individual}
                    key={entityId}
                    refetch={entityRefetch}
                  />
                ),
                files: (
                  <EntityFiles
                    entityId={entityId}
                    entityType={entityType}
                    documents={currentEntity?.documents}
                  />
                ),
                history: (
                  <EntityHistory
                    entity={entity[entityType]}
                    entityType={entityType}
                  />
                ),
                fees: <FeesTab licenseId={entityId} />,
              };

              return tabContent[tab ?? "license"] || null;
            })()}
          </div>
        </div>

        {/* Buttons */}
        <div className="order-first flex h-fit w-full shrink-0 flex-col gap-10 sm:flex-row lg:order-last lg:w-[300px] lg:flex-col">
          {/* title */}
          <div className="relative flex w-full flex-col rounded border bg-white p-4 pt-8">
            {/* Renewal / Resubmit Section */}
            {status === "Rejected" ? (
              <>
                <h3 className="font-base absolute left-2 top-0 -translate-y-1/2 rounded-sm bg-red-700 px-2 text-neutral-50">
                  Resubmission Required
                </h3>
                <ResubmitButton license={currentEntity} />
              </>
            ) : renewalTable[status] ? (
              <>
                <h3 className="font-base absolute left-2 top-0 -translate-y-1/2 rounded-sm bg-blue-700 px-2 text-neutral-50">
                  Renewal
                </h3>
                <div className="mt-6 space-y-1 text-sm text-neutral-700">
                  <p className="font-medium">
                    Renewal is not available at this time:
                  </p>
                  <p className="font-semibold capitalize text-red-600">
                    {renewalTable[status]}
                  </p>
                </div>
              </>
            ) : (
              <>
                <h3 className="font-base absolute left-2 top-0 -translate-y-1/2 rounded-sm bg-blue-700 px-2 text-neutral-50">
                  Renewal
                </h3>
                {entityIsFetching ? (
                  <div className="space-y-1 text-sm text-neutral-700">
                    <p className="font-medium">Loading...</p>
                    <p>Getting Renewal Information</p>
                  </div>
                ) : actions.includes("addToCart") ||
                  actions.includes("canDelete") ? null : (
                  <div className="space-y-3">
                    <div className="rounded-lg bg-white">
                      <RenewSelect license={entity} actions={actions} />
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
          <div className="relative flex w-full flex-col rounded border bg-white p-4 pt-8">
            {/* Discounts */}
            <h3 className="font-base absolute left-2 top-0 -translate-y-1/2 rounded-sm bg-neutral-700 px-2 text-neutral-50">
              Actions
            </h3>

            {/* Buttons */}
            <div className="flex w-full flex-col gap-4">
              {actions.includes("addToCart") && (
                <AddToCartButton entityId={entityId} />
              )}
              {/* Delete Draft License */}
              {actions.includes("canDelete") && (
                <DeleteButton entityId={entityId} refetch={entityRefetch} />
              )}

              {/* Undo License Renewal */}
              {actions.includes("canCancelRenewal") && (
                <UndoRenewalButton
                  entityId={entityId}
                  refetch={entityRefetch}
                />
              )}

              {!isPendingLicense && (
                <DownloadLicenseButton2 licenseId={entityId} />
              )}

              {isDog() && hasPermissions(["super-admin"]) && (
                <Button
                  className="flex items-center gap-2"
                  onClick={() => {
                    addToCart("def36506-69ea-11ee-8c99-0242ac120002", "tag");
                  }}
                >
                  <FaTag className="" />
                  Add Replacement Dog Tag
                </Button>
              )}

              {permitted && <ReportIssue license={currentEntity} />}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

const DeleteButton = ({
  entityId,
  refetch,
}: {
  entityId: string;
  refetch: any;
}) => {
  const { back } = useRouter();
  const deleteLicense = useDeleteLicense2();
  const queryClient = useQueryClient();
  const [_, setToast] = useAtom(toastAtom);

  return (
    <Button
      variant="destructive"
      onClick={() => {
        deleteLicense.mutate(entityId, {
          onSuccess: () => {
            setToast({
              status: "success",
              label: "License Deleted Successfully",
            });
            queryClient.invalidateQueries(["license2"]);
            queryClient.invalidateQueries(["activeCart"]);
            refetch();
            back();
          },
          onError: (error: any) => {
            setToast({
              status: "error",
              label: "License Could Not Be Deleted",
            });
            console.error(error);
          },
        });
      }}
      disabled={deleteLicense.isLoading}
      className="flex items-center justify-center gap-2"
    >
      {deleteLicense.isLoading ? (
        <>
          <CgSpinner className="animate-spin text-2xl" />
          Deleting License
        </>
      ) : (
        <>
          <LuTrash />
          Cancel Draft License
        </>
      )}
    </Button>
  );
};

const UndoRenewalButton = ({
  entityId,
  refetch,
}: {
  entityId: string;
  refetch: any;
}) => {
  const router = useRouter();
  const deleteLicense = useDeleteLicense2();
  const queryClient = useQueryClient();
  const [_, setToast] = useAtom(toastAtom);

  return (
    <Button
      onClick={() => {
        deleteLicense.mutate(entityId, {
          onSuccess: () => {
            setToast({
              status: "success",
              label: "License Renewal Undone Successfully",
            });
            queryClient.invalidateQueries(["license2"]);
            queryClient.invalidateQueries(["activeCart"]);
            refetch();
            router.refresh();
          },
          onError: (error: any) => {
            setToast({
              status: "error",
              label: "License Could Not Be Undone",
            });
            console.error(error);
          },
        });
      }}
      disabled={deleteLicense.isLoading}
      className="flex items-center justify-center gap-2"
    >
      {deleteLicense.isLoading ? (
        <>
          <CgSpinner className="animate-spin text-2xl" />
          Undoing Renewal
        </>
      ) : (
        <>
          <LuUndo />
          Undo Renewal
        </>
      )}
    </Button>
  );
};

const AddToCartButton = ({ entityId }: { entityId: string }) => {
  const { addToCart } = useMyCart();

  return (
    <Button
      variant="primary"
      onClick={() => {
        addToCart(entityId, "license");
      }}
      className="flex items-center justify-center gap-2 "
    >
      <FiShoppingCart />
      Add to Cart
    </Button>
  );
};

const ReportIssue = ({ license }: { license: License }) => {
  const [user] = useAtom(userAtom);
  const {
    data: redmineApiKey,
    isLoading: isLoadingRedmineApiKey,
    isError: isErrorRedmineApiKey,
  } = useGetRedmineProjectId();
  const [supportModal, setSupportModal] = useState<boolean>(false);

  if (isLoadingRedmineApiKey) return <div>Loading...</div>;
  if (isErrorRedmineApiKey) return <div>Error Loading Redmine API Key</div>;

  return (
    <>
      <Button
        onClick={() => {
          setSupportModal(true);
        }}
        variant={"destructive"}
        className="flex items-center gap-2"
        disabled={!redmineApiKey}
      >
        <BiSupport />
        {redmineApiKey ? "Report Issue" : "Report Issue (Access Required)"}
      </Button>
      {supportModal && (
        <Modal
          title={"Create Support Ticket"}
          isOpen={supportModal}
          onClose={() => {
            setSupportModal(false);
          }}
          disableOutsideClick={true}
        >
          <NewTicket
            onClose={() => {
              setSupportModal(false);
            }}
            subject={`License Issue: ${license.licenseNumber} - ${license.licenseType?.name}`}
            priority={3}
          />
        </Modal>
      )}
    </>
  );
};

const ResubmitButton = ({ license }: { license: License }) => {
  const rejectedEntities = getRejectedFields(license);
  const { entityRefetch } = useEntity();
  const resubmitLicense = useResubmitLicense();
  const [_, setToast] = useAtom(toastAtom);
  const { replace } = useRouter();

  const resubmit = () => {
    resubmitLicense.mutate(license.entityId, {
      onSuccess: () => {
        entityRefetch();
        setToast({
          status: "success",
          label: "License Resubmitted Successfully",
          message: "License has been resubmitted successfully.",
        });
      },
      onError: () => {
        setToast({
          status: "error",
          label: "Resubmit License Failed",
          message: "License could not be resubmitted. Please try again.",
        });
      },
    });
  };

  const deniedComment = license.deniedComment || null;

  return (
    <div className="flex flex-col gap-2">
      {rejectedEntities.length > 0 && (
        <div className="mt-4 rounded-md border border-red-200 bg-red-50 p-4">
          <p className="text-sm font-medium text-red-600">
            {deniedComment ||
              "There are outstanding corrections that need to be made before resubmitting your license."}
          </p>
          <button
            className="mt-2 text-sm text-blue-500 underline"
            onClick={() => {
              const currentParams = new URLSearchParams();
              currentParams.set("tab", rejectedEntities[0].entityType);
              replace(`?${currentParams.toString()}`);
            }}
          >
            Go to Correction
          </button>
        </div>
      )}
      <Button
        onClick={resubmit}
        variant="primary"
        className={`flex w-full items-center justify-center gap-2 rounded-md py-2 font-semibold ${
          resubmitLicense.isLoading
            ? "bg-gray-300 text-gray-600"
            : "bg-blue-500 text-white hover:bg-blue-600"
        }`}
        disabled={resubmitLicense.isLoading || rejectedEntities.length > 0}
      >
        {resubmitLicense.isLoading ? (
          <>
            <CgSpinner className="animate-spin text-xl" />
            Resubmitting License
          </>
        ) : (
          <>
            <LuRedo2 size={18} />
            Resubmit License
          </>
        )}
      </Button>
    </div>
  );
};

function getRejectedFields(currentEntity: any): Array<{
  entityId: string;
  name: string;
  rejectedFields: string[];
  entityType: string;
}> {
  const rejectedEntities: Array<{
    entityId: string;
    entityType: string;
    name: string;
    rejectedFields: string[];
  }> = [];

  // Helper function to process entities
  const processEntities = (
    entities: any[],
    entityType: string,
    getName: (entity: any) => string,
  ) => {
    for (const entity of entities) {
      const filteredFields = entity.rejectedFields || [];
      if (filteredFields.length > 0) {
        rejectedEntities.push({
          entityId: entity.entityId,
          entityType,
          name: getName(entity),
          rejectedFields: filteredFields,
        });
      }
    }
  };

  // Process the main entity
  const mainFilteredFields = currentEntity.rejectedFields || [];
  if (mainFilteredFields.length > 0) {
    rejectedEntities.push({
      entityId: currentEntity.entityId,
      entityType: currentEntity.entityType,
      name: currentEntity.name || "Main Entity",
      rejectedFields: mainFilteredFields,
    });
  }

  // Process dogs and individuals
  if (Array.isArray(currentEntity.dogs)) {
    processEntities(
      currentEntity.dogs,
      "dog",
      (entity) => entity.dogName || "Dog Entity",
    );
  }
  if (Array.isArray(currentEntity.individuals)) {
    processEntities(
      currentEntity.individuals,
      "individual",
      (entity) => entity.firstName || "Individual Entity",
    );
  }

  return rejectedEntities;
}
