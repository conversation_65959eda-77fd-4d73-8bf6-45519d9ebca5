"use client";
import {
  useDeactivateResident,
  useDeleteResidentProfile,
  useGetProfile,
  useResidentGetProfile,
  useUpdateResidentProfile,
} from "@/hooks/api/useProfiles";
import { Card, CardHeader } from "@/components/ui/card";
import PageContainer from "@/components/ui/Page/PageContainer";
import { motion } from "framer-motion";
import { Title } from "@/components/profile/ProfileComponents";
import { Section } from "@/app/(app)/(realm)/(protected)/(app)/entity/[entitytype]/[entityId]/profile/Dialogs";
import { Individual } from "@/types/IndividualType";
import { Switch } from "@/components/ui/switch";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";
import { useQueryClient } from "@tanstack/react-query";
import { createFormData } from "../components/modals/EditDialog";
import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  DialogTit<PERSON>,
  Dialog<PERSON>rigger,
} from "@/components/ui/dialog";
import { useState } from "react";
import { useGetMyProfile } from "@/hooks/api/useResident";
import { OptInStateType } from "@/app/(app)/(realm)/(protected)/(app)/entity/[entitytype]/[entityId]/settings/page";

type Params = {
  [key: string]: any;
};

// Function to format the optIns array
const formatOptIns = (optIns: any) => {
  const formattedOptIns: OptInStateType = optIns.map((a: any) => {
    return {
      [a.name]: a.active,
    };
  });

  return formattedOptIns;
};

export default function Page({
  params: { entityId, entityType },
}: {
  params: { entityId: string; entityType: string; realm: string };
}) {
  const [open, setOpen] = useState(false);

  const isNotIndividual = entityType !== "individual";

  const {
    data: resident,
    isLoading: residentIsLoading,
    isError: residentIsError,
  } = useGetMyProfile();

  const {
    data: regData,
    isLoading: regIsLoading,
    isError: regIsError,
  } = useResidentGetProfile(entityType, entityId, {}, isNotIndividual);

  const data = isNotIndividual ? regData : resident;
  const isLoading = isNotIndividual ? regIsLoading : residentIsLoading;
  const isError = isNotIndividual ? regIsError : residentIsError;

  const { individual } = data;

  const updateResidentProfile = useUpdateResidentProfile();
  const deactivateProfile = useDeactivateResident(entityId);
  const deleteResidentOnlineAccount = useDeleteResidentProfile();
  const [, setToast] = useAtom(toastAtom);
  const queryClient = useQueryClient();

  const accountActive = individual.active;

  const [entityStatus, setEntityStatus] = useState(accountActive);
  const [entityOptIns, setEntityOptIns] = useState<OptInStateType>(
    formatOptIns(individual.optIns),
  );

  const handleSave = (fieldName: string, value: any, params?: Params) => {
    let data = {};

    if (params) {
      data = { ...params };
    } else data = { [fieldName]: value };

    const formData = createFormData(data);

    updateResidentProfile.mutate(
      {
        entityId: individual.entityId,
        body: formData,
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries();
          // setToast({
          //   status: "success",
          //   label: "Individual Updated",
          //   message: "Successfully Updated Individual Information",
          // });
        },
        onError: (error: any) => {
          console.log(error);
          setToast({
            status: "error",
            label: "Error Updating Profile",
            message: error?.response?.data?.message,
          });
          setEntityOptIns((prev) => {
            return prev.map((a) => {
              return {
                ...a,
                [fieldName]: !value,
              };
            });
          });
        },
      },
    );
  };

  if (isLoading) {
    return (
      <div className="container">
        <Card>
          <CardHeader>
            <h1>Loading Settings...</h1>
          </CardHeader>
        </Card>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="container">
        <PageContainer>
          <h1>Error Loading Page</h1>
        </PageContainer>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, x: -100 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -100 }}
      className="md:mx-auto flex flex-col gap-10 md:container"
    >
      <PageContainer className="rounded-none md:rounded pb-20">
        <div className="flex flex-col gap-10 xl:flex-row">
          <div className="flex w-full flex-col gap-7">
            <div className="flex w-full flex-col gap-20">
              {/* Settings */}
              <div>
                <Title>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span>Document Management</span>
                    </div>
                  </div>
                </Title>

                <div className="flex flex-col gap-4">
                  {optIns.map((a) => {
                    const checked = entityOptIns.find(
                      (b) => b[a?.key] === true,
                    ) as any;

                    return (
                      <Section label={a.label} key={a.key}>
                        <Switch
                          checked={checked}
                          onCheckedChange={(value) => {
                            const newOptIns = entityOptIns.map((b) => {
                              if (b[a.key]) {
                                return {
                                  [a.key]: value,
                                };
                              }
                              return b;
                            });

                            setEntityOptIns(newOptIns);
                            handleSave(a.key, value);
                          }}
                        />
                      </Section>
                    );
                  })}
                </div>
              </div>

              {/* Danger Zone */}
              <div className="rounded-lg border-2 border-red-600 bg-red-50/60 p-4">
                <Title>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span>Permanent Settings</span>
                    </div>
                  </div>
                </Title>

                <div className="flex flex-col gap-4">
                  <Dialog open={open} onOpenChange={setOpen}>
                    <DialogTrigger asChild>
                      <Button
                        className="w-fit hover:bg-red-400"
                        size="sm"
                        variant="destructive"
                        onClick={() => setOpen(true)}
                      >
                        Delete Online Account
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Are you absolutely sure?</DialogTitle>
                        <DialogDescription>
                          This action cannot be undone. This will delete your
                          online account
                        </DialogDescription>
                      </DialogHeader>
                      <DialogFooter>
                        <Button
                          variant="outline"
                          onClick={() => setOpen(false)}
                        >
                          Cancel
                        </Button>
                        <Button
                          variant="destructive"
                          onClick={() => {
                            console.log("Delete Account");
                            deleteResidentOnlineAccount.mutate(
                              {
                                entityId: individual.entityId,
                              },
                              {
                                onSuccess: () => {
                                  queryClient.invalidateQueries();
                                  setToast({
                                    status: "success",
                                    label: "Account Deleted",
                                    message: "Successfully Deleted Account",
                                  });
                                  setOpen(false);
                                },
                                onError: (error: any) => {
                                  console.log(error);
                                  setToast({
                                    status: "error",
                                    label: "Error Deleting Account",
                                    message: error?.response?.data?.message,
                                  });
                                },
                              },
                            );
                          }}
                          className="hover:bg-red-400"
                        >
                          Continue
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>
            </div>
          </div>
        </div>
      </PageContainer>
    </motion.div>
  );
}

const optIns: {
  key: string;
  label: string;
}[] = [
  {
    key: "optInPaperless",
    label: "Paperless",
  },
  // {
  //   key: "optInLicenseUpdates",
  //   label: "License Updates",
  // },
  // {
  //   key: "optInDogUpdates",
  //   label: "Dog Updates",
  // },
];
