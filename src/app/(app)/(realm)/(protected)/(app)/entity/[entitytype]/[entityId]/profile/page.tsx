"use client";
import { useGetProfile } from "@/hooks/api/useProfiles";
import IndividualProfile from "./individual/IndividualProfile";
import DogProfile from "./dog/DogProfile";
import PageContainer from "@/components/ui/Page/PageContainer";

export default function ProfilePage({
  params: { entityId, entitytype, realm },
}: {
  params: { entityId: string; entitytype: string; realm: string };
}) {

  const { data, isLoading, isError, refetch } = useGetProfile(
    entitytype as string,
    entityId as string,
  );

  if (isLoading) return <div>loading...</div>;
  if (isError) return <div>Error loading profile data</div>;

  if (data) {
    if(entitytype === 'individual') {
      return (
        <div className="container mx-auto">
          <PageContainer>
            <IndividualProfile individual={data.individual} refetch={refetch} />
          </PageContainer>
        </div>
      );
    } else if(entitytype === 'dog') {
      return (
        <div className="container mx-auto">
          <PageContainer>
            <DogProfile dog={data.dog} />
          </PageContainer>
        </div>
      );
    } 

    return null
  }

  return <div>No Profile Information Available</div>;
}
