const build: {
  [key: string]: {
    url: string;
    clientId: string;
  };
} = {
  development: {
    url: "https://auth-dev.clerkxpress.com/",
    clientId: "clerkXpress-frontend",
  },
  production: {
    url: "https://auth.clerkxpress.com/",
    clientId: "clerkXpress-frontend",
  },
  staging: {
    url: "https://auth-staging.clerkxpress.com/",
    clientId: "clerkXpress-frontend",
  },
  local: {
    url: "http://localhost:8080/",
    clientId: "clerkXpress-frontend",
  },
  default: {
    url: "https://auth-dev.clerkxpress.com/",
    clientId: "clerkXpress-frontend-local",
  },
};

export const getKeycloakConfig = () => {
  const appEnvironment = process.env.NEXT_PUBLIC_APP_ENV || "default";
  return build[appEnvironment] || build.default;
};

