package com.scube.licensing.features.profile.dto;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.scube.licensing.features.entity.dtos.IEntityDto;
import com.scube.licensing.features.profile.dto.config.IAssociableDto;
import lombok.Data;
import org.springframework.util.ObjectUtils;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Data
public class AddressDto implements IEntityDto, IAssociableDto {
    private UUID entityId;
    private List<EventDto> events;
    private String houseNumber;
    private String streetName;
    private String streetAddress;
    private String streetAddress2;
    private String town;
    private String city;
    private String state;
    private String zip;
    private String fullAddress;
    private Double latitude;
    private Double longitude;
    @JsonIgnore
    private Long addressId;
    @JsonIgnore
    private Map<String, Object> customFields;
    @JsonIgnore
    private String tableName;
    private String createdBy;
    private String lastModifiedBy;
    private Instant createdDate;
    private Instant lastModifiedDate;

    @JsonAnyGetter
    public Map<String, Object> getCustomFields() {
        if (ObjectUtils.isEmpty(customFields)) customFields = new HashMap<>();
        return customFields.entrySet().stream()
                .filter(entry -> !entry.getKey().equalsIgnoreCase("entityId"))
                .collect(HashMap::new, (m, e) -> m.put(e.getKey(), e.getValue()), Map::putAll);
    }

    @Override
    public void putProperties(Map<String, Object> properties) {
        if (ObjectUtils.isEmpty(customFields)) customFields = new HashMap<>();
        customFields.putAll(properties);
    }
}