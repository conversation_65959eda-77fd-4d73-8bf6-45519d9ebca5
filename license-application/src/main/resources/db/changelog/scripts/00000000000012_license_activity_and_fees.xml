<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="davidr (generated)" id="1695002604577-125">
        <createTable tableName="license_activity">
            <column autoIncrement="true" name="license_activity_id" startWith="2" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="license_activity_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="activity_type" type="VARCHAR(255)"/>
            <column name="valid_from_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="valid_to_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="license_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-128">
        <addColumn tableName="license_activity_fee">
            <column autoIncrement="true" name="license_activity_fee_id" type="bigserial">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-129">
        <addColumn tableName="license">
            <column name="application_date" type="timestamptz"/>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-130">
        <addColumn tableName="license_activity_fee">
            <column name="payment_status" type="varchar(255 BYTE)"/>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-131">
        <addColumn tableName="license_activity_fee">
            <column name="license_activity_id" type="int8"/>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-132">
        <createIndex indexName="license_activity_fee_pkey" tableName="license_activity_fee" unique="true">
            <column name="license_activity_fee_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-133">
        <addForeignKeyConstraint baseColumnNames="license_activity_id" baseTableName="license_activity_fee"
                                 constraintName="fk3q03bys6i6gluehlfe9a80xr2" deferrable="false"
                                 initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="license_activity_id" referencedTableName="license_activity"
                                 validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-134">
        <addForeignKeyConstraint baseColumnNames="license_id" baseTableName="license_activity"
                                 constraintName="fk632xb116gmjr0no5rb8smylb6" deferrable="false"
                                 initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="license_id" referencedTableName="license" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-136">
        <dropColumn columnName="is_paid" tableName="license_activity_fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-137">
        <dropColumn columnName="license_id" tableName="license_activity_fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-138">
        <dropColumn columnName="license_fee_id" tableName="license_activity_fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-1">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="address"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-2">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="app_properties"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-3">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="association"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-4">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="contact"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-5">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="contact_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-6">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="contact_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-7">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="contact_type_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-8">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="custom_entity_instance"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-9">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="custom_entity_sub_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-10">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="custom_entity_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-11">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="custom_field_value"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-12">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="license"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-13">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="license_activity_fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-14">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="license_status"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-15">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="license_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-16">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="license_type_fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-17">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="license_type_fee_conditional"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-18">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="license_type_setting"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-19">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_argument"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-20">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_body_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-21">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_builder"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-22">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_conditional_display"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-23">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_element_options"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-24">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_form_element"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-25">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_on_form_submit_api"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-26">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_on_page_next_api"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-27">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_page"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-28">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_query_string"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-29">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_request_body"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-30">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_request_slug"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-31">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_response"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-32">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_response_error"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-33">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_response_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-34">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_response_success"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-35">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_section"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-36">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_validation"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-37">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="participant"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-38">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="participant_address"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-39">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="participant_address_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-40">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="participant_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-41">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="participant_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-42">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="participant_type_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-43">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="profile_form_argument"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-44">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="profile_form_argument_filter"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-45">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="profile_form_builder"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-46">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="profile_form_element"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-47">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="profile_form_section"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-48">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="profile_form_section_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-49">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="profile_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-50">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="search_form_argument"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-51">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="search_form_argument_filter"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-52">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="search_form_builder"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-53">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="search_form_element"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-54">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="search_form_section"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-55">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="table_column_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-56">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="table_custom_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-57">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="table_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-58">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="tenant"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-59">
        <modifyDataType columnName="enqueued_at" newDataType="timestamp" tableName="dead_letter_entry"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-60">
        <modifyDataType columnName="issued_date" newDataType="timestamp" tableName="license"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-61">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="address"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-62">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="app_properties"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-63">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="association"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-64">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="contact"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-65">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="contact_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-66">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="contact_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-67">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="contact_type_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-68">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="custom_entity_instance"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-69">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="custom_entity_sub_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-70">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="custom_entity_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-71">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="custom_field_value"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-72">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="license"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-73">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="license_activity_fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-74">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="license_status"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-75">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="license_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-76">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="license_type_fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-77">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp"
                        tableName="license_type_fee_conditional"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-78">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="license_type_setting"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-79">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_argument"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-80">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_body_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-81">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_builder"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-82">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp"
                        tableName="multi_form_conditional_display"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-83">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_element_options"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-84">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_form_element"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-85">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp"
                        tableName="multi_form_on_form_submit_api"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-86">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp"
                        tableName="multi_form_on_page_next_api"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-87">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_page"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-88">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_query_string"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-89">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_request_body"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-90">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_request_slug"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-91">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_response"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-92">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_response_error"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-93">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_response_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-94">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp"
                        tableName="multi_form_response_success"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-95">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_section"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-96">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_validation"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-97">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="participant"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-98">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="participant_address"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-99">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="participant_address_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-100">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="participant_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-101">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="participant_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-102">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="participant_type_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-103">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="profile_form_argument"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-104">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp"
                        tableName="profile_form_argument_filter"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-105">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="profile_form_builder"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-106">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="profile_form_element"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-107">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="profile_form_section"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-108">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="profile_form_section_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-109">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="profile_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-110">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="search_form_argument"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-111">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp"
                        tableName="search_form_argument_filter"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-112">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="search_form_builder"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-113">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="search_form_element"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-114">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="search_form_section"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-115">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="table_column_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-116">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="table_custom_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-117">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="table_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-118">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="tenant"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-119">
        <modifyDataType columnName="last_touched" newDataType="timestamp" tableName="dead_letter_entry"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-120">
        <modifyDataType columnName="processing_started" newDataType="timestamp" tableName="dead_letter_entry"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-121">
        <modifyDataType columnName="valid_from_date" newDataType="timestamp" tableName="license"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-122">
        <modifyDataType columnName="valid_to_date" newDataType="timestamp" tableName="license"/>
    </changeSet>

</databaseChangeLog>
