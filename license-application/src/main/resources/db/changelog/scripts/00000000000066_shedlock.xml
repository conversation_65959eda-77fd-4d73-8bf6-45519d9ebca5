<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="createTable_shedlock" author="David">
        <createTable tableName="shedlock">
            <column name="name" type="VARCHAR(64)">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="lock_until" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="locked_at" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="locked_by" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>