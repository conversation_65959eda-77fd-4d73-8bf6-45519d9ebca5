CREATE OR REPLACE FUNCTION associate_parent_child
(
    in fn_parent_type character varying,
    in fn_parent_id bigint,
    in fn_child_type character varying,
    in fn_child_id bigint,
    in fn_audit_user varchar(2000) default 'fn',
    in fn_audit_date timestamp default now(),
    in fn_conversion_reference varchar(255) default '',
    in fn_audit_log_revision_id bigint default null
)
RETURNS void
AS $$
DECLARE
    result_id integer;
    var_association_exists integer;
BEGIN
    if coalesce(fn_audit_log_revision_id, 0) <= 0 then
        insert into audit_log_revision(audit_log_revision_id, "timestamp", auditor)
        values ((select max(audit_log_revision_id)+1 from audit_log_revision),
            extract(epoch from now())::bigint, fn_conversion_reference
        )
        returning audit_log_revision_id into fn_audit_log_revision_id;
    end if;

    Select association_id
    from association
    into var_association_exists
    where child_association_type = fn_child_type
    and child_id = fn_child_id
    and parent_association_type = fn_parent_type
    and parent_id = fn_parent_id;

    if coalesce(var_association_exists, 0) <= 0 then
        --parent to child insert
        insert into public.association(created_by, created_date, last_modified_by, last_modified_date,
                                    child_association_type, child_id, parent_association_type, parent_id, conversion_reference)
        values(
            fn_audit_user, fn_audit_date, fn_audit_user, fn_audit_date,
            fn_child_type, fn_child_id,
            fn_parent_type, fn_parent_id,
            fn_conversion_reference
        )
        returning association_id into result_id;

        insert into public.audit_log_association(last_modified_by, last_modified_date,
                                        child_association_type, child_id, parent_association_type, parent_id, conversion_reference,
                                            revision_id, revision_type, association_id)
        select
            last_modified_by, last_modified_date,
            child_association_type, child_id, parent_association_type, parent_id, conversion_reference,
            fn_audit_log_revision_id, '0', result_id
        from association
        where association_id = result_id;
    end if;


    Select association_id
    from association
    into var_association_exists
    where child_association_type = fn_parent_type
    and child_id = fn_parent_id
    and parent_association_type = fn_child_type
    and parent_id = fn_child_id;


     if coalesce(var_association_exists, 0) <= 0 then

        --child to parent insert
        insert into public.association(created_by, created_date, last_modified_by, last_modified_date,
                                        child_association_type, child_id, parent_association_type, parent_id, conversion_reference)
        values(
            fn_audit_user, fn_audit_date, fn_audit_user, fn_audit_date,
            fn_parent_type, fn_parent_id,
            fn_child_type, fn_child_id,
            fn_conversion_reference
        )
        returning association_id into result_id;

        insert into public.audit_log_association(last_modified_by, last_modified_date,
                                        child_association_type, child_id, parent_association_type, parent_id, conversion_reference,
                                            revision_id, revision_type, association_id)
        select
            last_modified_by, last_modified_date,
            child_association_type, child_id, parent_association_type, parent_id, conversion_reference,
            fn_audit_log_revision_id, '0', result_id
        from association
        where association_id = result_id;
    end if;
END;
$$ LANGUAGE plpgsql;


--select * from associate_parent_child(1, 'PARTICIPANT', 1, 'LICENSE', 'audit', now()::timestamp, 'conv', 2);