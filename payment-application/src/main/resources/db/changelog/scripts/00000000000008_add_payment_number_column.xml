<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.6.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.6.xsd">
    <changeSet id="addPaymentNumberColumn" author="Ben">
        <!-- Add the payment_number column to payment table -->
        <addColumn tableName="payment">
            <column name="payment_number" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet id="updatePaymentNumbers" author="Ben">
        <!-- Update existing rows to generate payment_numbers based on id -->
        <sql>
            UPDATE payment
            SET payment_number = 'P-' || payment_id
            WHERE payment_number IS NULL;
        </sql>
    </changeSet>
    <!-- Alter the payment_number column to make it not nullable and unique -->
    <changeSet id="alterPaymentNumberColumn" author="Ben">
        <sql>
            ALTER TABLE payment
            ALTER COLUMN payment_number SET NOT NULL;
        </sql>
        <sql>
            ALTER TABLE payment
            ADD CONSTRAINT unique_payment_number UNIQUE (payment_number);
        </sql>
    </changeSet>
    <changeSet id="createGeneratePaymentNumberFunction" author="Ben">
        <!-- Create the trigger function to generate the payment_number -->
        <sql>
            CREATE OR REPLACE FUNCTION generate_payment_number()
            RETURNS TRIGGER
            LANGUAGE plpgsql
            AS'
            BEGIN
                NEW.payment_number := ''P-'' || NEW.payment_id;
                RETURN NEW;
            END;'
        </sql>
    </changeSet>
    <changeSet id="createSetPaymentNumberTrigger" author="Ben">
        <!-- Create the trigger to call the generate_payment_number function -->
        <sql>
            CREATE OR REPLACE TRIGGER set_payment_number
            BEFORE INSERT ON payment
            FOR EACH ROW
            EXECUTE FUNCTION generate_payment_number();
        </sql>
    </changeSet>
    <changeSet id="addPaymentNumberColumnToAuditTable" author="Ben">
        <!-- Add the payment_number column to payment table -->
        <addColumn tableName="audit_log_payment">
            <column name="payment_number" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet id="updatePaymentNumbersOnAuditTable" author="Ben">
        <!-- Update existing rows to generate payment_numbers based on id -->
        <sql>
            UPDATE audit_log_payment
            SET payment_number = 'P-' || payment_id
            WHERE payment_number IS NULL;
        </sql>
    </changeSet>
</databaseChangeLog>
